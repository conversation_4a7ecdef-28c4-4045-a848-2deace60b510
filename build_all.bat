@echo off
echo SMS状态报告更新程序 - 多平台构建脚本
echo.

set CGO_ENABLED=0

echo 构建 Linux AMD64...
set GOOS=linux
set GOARCH=amd64
go build -o update_sms_record_linux_amd64 .
if %ERRORLEVEL% EQU 0 (
    echo ✅ Linux AMD64 构建成功
) else (
    echo ❌ Linux AMD64 构建失败
)

echo.
echo 构建 Linux ARM64...
set GOOS=linux
set GOARCH=arm64
go build -o update_sms_record_linux_arm64 .
if %ERRORLEVEL% EQU 0 (
    echo ✅ Linux ARM64 构建成功
) else (
    echo ❌ Linux ARM64 构建失败
)

echo.
echo 构建 Windows AMD64...
set GOOS=windows
set GOARCH=amd64
go build -o update_sms_record_windows_amd64.exe .
if %ERRORLEVEL% EQU 0 (
    echo ✅ Windows AMD64 构建成功
) else (
    echo ❌ Windows AMD64 构建失败
)

echo.
echo 构建 macOS AMD64...
set GOOS=darwin
set GOARCH=amd64
go build -o update_sms_record_macos_amd64 .
if %ERRORLEVEL% EQU 0 (
    echo ✅ macOS AMD64 构建成功
) else (
    echo ❌ macOS AMD64 构建失败
)

echo.
echo 构建 macOS ARM64 (Apple Silicon)...
set GOOS=darwin
set GOARCH=arm64
go build -o update_sms_record_macos_arm64 .
if %ERRORLEVEL% EQU 0 (
    echo ✅ macOS ARM64 构建成功
) else (
    echo ❌ macOS ARM64 构建失败
)

echo.
echo 重置环境变量...
set GOOS=
set GOARCH=
set CGO_ENABLED=

echo.
echo 🎉 构建完成！生成的文件：
echo - update_sms_record_linux_amd64     (Linux 64位)
echo - update_sms_record_linux_arm64     (Linux ARM64)
echo - update_sms_record_windows_amd64.exe (Windows 64位)
echo - update_sms_record_macos_amd64     (macOS Intel)
echo - update_sms_record_macos_arm64     (macOS Apple Silicon)
echo.
echo 使用说明：
echo 1. 选择对应平台的二进制文件
echo 2. Linux/macOS: chmod +x filename 添加执行权限
echo 3. 运行: ./filename your_csv_file.csv
echo.
pause
