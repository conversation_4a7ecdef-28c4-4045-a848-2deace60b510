package main

import (
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"strings"
)

// ParseCSVFile 解析CSV文件
func ParseCSVFile(filename string) ([]CSVRecord, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("无法打开CSV文件: %v", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = ','
	reader.TrimLeadingSpace = true
	reader.LazyQuotes = true

	var records []CSVRecord
	lineNum := 0

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("读取CSV文件第%d行时出错: %v", lineNum+1, err)
		}

		lineNum++

		// 确保记录有足够的列
		if len(record) < 11 {
			fmt.Printf("警告：第%d行列数不足，跳过\n", lineNum)
			continue
		}

		// 提取所需字段并去除前后空格和制表符
		csvRecord := CSVRecord{
			Seq:       trimAllWhitespace(record[0]),  // 第1列
			ReportSTM: trimAllWhitespace(record[1]),  // 第2列
			Mobile:    trimAllWhitespace(record[2]),  // 第3列
			ReportCHN: trimAllWhitespace(record[5]),  // 第6列
			ReportSC:  trimAllWhitespace(record[6]),  // 第7列
			ReportST:  trimAllWhitespace(record[7]),  // 第8列
			BatchNo:   trimAllWhitespace(record[10]), // 第11列
		}

		// 跳过第六列为"未知"的状态报告
		if csvRecord.ReportCHN == "未知" {
			fmt.Printf("跳过未知状态报告，行号: %d\n", lineNum)
			continue
		}

		records = append(records, csvRecord)
	}

	fmt.Printf("成功解析CSV文件，共处理%d行，有效记录%d条\n", lineNum, len(records))
	return records, nil
}

// trimAllWhitespace 去除字符串前后的所有空白字符（包括空格、制表符、换行符等）
func trimAllWhitespace(s string) string {
	return strings.TrimSpace(s)
}
