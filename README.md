# SMS状态报告更新程序

这是一个用Go语言编写的程序，用于解析状态报告CSV文件并更新短信发送记录数据表。

## 功能说明

程序会读取CSV格式的状态报告文件，并根据以下规则更新数据库中的发送记录：

1. 跳过第六列为"未知"的状态报告
2. 根据mobile、seq和batch_no查询hg_dx_sms_send_record_202507表是否存在发送记录，不存在则忽略
3. 判断created_result是否为3，不等于3则忽略
4. 当第七列为"DELIVRD"时，将该条发送记录的created_result更新为1，否则更新为2，同时更新report_stm、report_chn、report_st和report_sc字段

## CSV文件格式

CSV文件应包含以下列（按顺序）：
- 第1列：seq（消息编号）
- 第2列：report_stm（发送时间）
- 第3列：mobile（手机号码）
- 第6列：report_chn（中文状态标识）
- 第7列：report_sc（状态报告）
- 第8列：report_st（状态时间）
- 第11列：batch_no（批次号）

## 数据库配置

程序已内置数据库连接配置，可直接使用。如需修改数据库连接，可通过环境变量覆盖：

```bash
export DB_HOST=***************
export DB_PORT=44944
export DB_USERNAME=dxpt
export DB_PASSWORD=Rf&z6&Na7aWG
export DB_DATABASE=dxpt
```

默认配置已设置为项目数据库，通常无需修改。

## 使用方法

1. 安装依赖：
```bash
go mod tidy
```

2. 运行程序：
```bash
go run . status_report.csv
```

或者编译后运行：
```bash
go build -o update_sms_record
./update_sms_record status_report.csv
```

## 示例

```bash
# 直接运行程序（使用内置数据库配置）
go run . status_report.csv

# 或使用Windows批处理脚本
run.bat status_report.csv

# 测试CSV解析功能
go run . test
```

## 输出说明

程序会输出详细的处理日志，包括：
- 每条记录的处理状态
- 跳过的记录及原因
- 更新成功的记录
- 最终的统计信息

## 注意事项

1. 确保CSV文件格式正确，列数足够
2. 确保数据库连接配置正确
3. 程序会自动跳过不符合条件的记录
4. 建议在生产环境使用前先在测试环境验证
