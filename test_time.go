package main

import (
	"fmt"
	"log"
)

func main() {
	// 测试时间解析
	testTimeStr := "20250731185123"
	fmt.Printf("原始时间字符串: %s\n", testTimeStr)
	
	parsedTime, err := ParseTimeString(testTimeStr)
	if err != nil {
		log.Fatalf("时间解析失败: %v", err)
	}
	
	fmt.Printf("解析后的时间: %s\n", parsedTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("预期时间应该是: 2025-07-31 10:51:23 (减去8小时)\n")
	
	// 测试另一个时间
	testTimeStr2 := "20250731190644"
	fmt.Printf("\n原始时间字符串: %s\n", testTimeStr2)
	
	parsedTime2, err := ParseTimeString(testTimeStr2)
	if err != nil {
		log.Fatalf("时间解析失败: %v", err)
	}
	
	fmt.Printf("解析后的时间: %s\n", parsedTime2.Format("2006-01-02 15:04:05"))
	fmt.Printf("预期时间应该是: 2025-07-31 11:06:44 (减去8小时)\n")
}
