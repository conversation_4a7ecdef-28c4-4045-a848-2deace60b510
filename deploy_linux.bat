@echo off
echo SMS状态报告更新程序 - Linux部署脚本
echo.

echo 清理旧文件...
if exist update_sms_record_linux del update_sms_record_linux
if exist deploy_package.tar.gz del deploy_package.tar.gz

echo.
echo 设置构建环境...
set GOOS=linux
set GOARCH=amd64
set CGO_ENABLED=0

echo 构建Linux二进制文件...
go build -ldflags="-s -w" -o update_sms_record_linux .

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 构建失败！
    goto :cleanup
)

echo ✅ 构建成功！

echo.
echo 创建部署包...
echo 包含以下文件：
echo - update_sms_record_linux (主程序)
echo - README.md (使用说明)
echo - .env.example (配置示例)

rem 创建临时目录
mkdir deploy_temp 2>nul
copy update_sms_record_linux deploy_temp\
copy README.md deploy_temp\
copy .env.example deploy_temp\

echo.
echo 生成Linux部署脚本...
echo #!/bin/bash > deploy_temp\install.sh
echo echo "SMS状态报告更新程序 - 安装脚本" >> deploy_temp\install.sh
echo echo "" >> deploy_temp\install.sh
echo echo "设置执行权限..." >> deploy_temp\install.sh
echo chmod +x update_sms_record_linux >> deploy_temp\install.sh
echo echo "✅ 安装完成！" >> deploy_temp\install.sh
echo echo "" >> deploy_temp\install.sh
echo echo "使用方法:" >> deploy_temp\install.sh
echo echo "./update_sms_record_linux your_csv_file.csv" >> deploy_temp\install.sh
echo echo "" >> deploy_temp\install.sh

echo.
echo 📦 部署包已准备完成！
echo.
echo 部署步骤：
echo 1. 将 deploy_temp 文件夹中的所有文件上传到Linux服务器
echo 2. 在服务器上运行: chmod +x install.sh
echo 3. 运行安装脚本: ./install.sh
echo 4. 使用程序: ./update_sms_record_linux your_csv_file.csv
echo.

:cleanup
echo 重置环境变量...
set GOOS=
set GOARCH=
set CGO_ENABLED=

echo.
pause
