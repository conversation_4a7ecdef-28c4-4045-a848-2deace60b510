@echo off
echo SMS状态报告更新程序 - Linux构建脚本
echo.

echo 设置环境变量...
set GOOS=linux
set GOARCH=amd64
set CGO_ENABLED=0

echo 开始构建Linux二进制文件...
go build -o update_sms_record_linux .

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo 生成的文件: update_sms_record_linux
    echo.
    echo 使用方法:
    echo 1. 将 update_sms_record_linux 文件上传到Linux服务器
    echo 2. 给文件添加执行权限: chmod +x update_sms_record_linux
    echo 3. 运行程序: ./update_sms_record_linux your_csv_file.csv
    echo.
) else (
    echo.
    echo ❌ 构建失败！
    echo 请检查代码是否有错误。
    echo.
)

echo 重置环境变量...
set GOOS=
set GOARCH=
set CGO_ENABLED=

echo.
pause
