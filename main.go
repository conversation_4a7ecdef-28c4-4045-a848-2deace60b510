package main

import (
	"fmt"
	"log"
	"os"
)

func main() {
	// 检查命令行参数
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run . <csv文件路径>")
		fmt.Println("示例: go run . status_report.csv")
		os.Exit(1)
	}

	csvFilePath := os.Args[1]

	// 检查CSV文件是否存在
	if _, err := os.Stat(csvFilePath); os.IsNotExist(err) {
		log.Fatalf("CSV文件不存在: %s", csvFilePath)
	}

	fmt.Printf("开始处理CSV文件: %s\n", csvFilePath)

	// 获取数据库配置
	config := GetDatabaseConfig()
	fmt.Printf("数据库配置: %s:%s@%s:%s/%s\n",
		config.Username, "****", config.Host, config.Port, config.Database)

	// 创建数据库管理器
	dbManager, err := NewDatabaseManager(config)
	if err != nil {
		log.Fatalf("创建数据库连接失败: %v", err)
	}
	defer dbManager.Close()

	fmt.Println("数据库连接成功")

	// 解析CSV文件
	records, err := ParseCSVFile(csvFilePath)
	if err != nil {
		log.Fatalf("解析CSV文件失败: %v", err)
	}

	// 处理每条记录
	processRecords(dbManager, records)

	fmt.Println("处理完成")
}

// processRecords 处理CSV记录
func processRecords(dbManager *DatabaseManager, records []CSVRecord) {
	var (
		totalCount    = len(records)
		processedCount = 0
		updatedCount   = 0
		skippedCount   = 0
		errorCount     = 0
	)

	fmt.Printf("开始处理%d条记录\n", totalCount)

	for i, record := range records {
		fmt.Printf("处理第%d/%d条记录: mobile=%s, seq=%s, batch_no=%s\n",
			i+1, totalCount, record.Mobile, record.Seq, record.BatchNo)

		// 查询发送记录是否存在
		sendRecord, err := dbManager.FindSendRecord(record.Mobile, record.Seq, record.BatchNo)
		if err != nil {
			fmt.Printf("查询发送记录失败: %v\n", err)
			errorCount++
			continue
		}

		if sendRecord == nil {
			fmt.Printf("发送记录不存在，跳过\n")
			skippedCount++
			continue
		}

		// 判断created_result是否为3
		if sendRecord.CreatedResult != 3 {
			fmt.Printf("created_result不等于3 (当前值: %d)，跳过\n", sendRecord.CreatedResult)
			skippedCount++
			continue
		}

		// 解析时间字符串
		reportSTM, err := ParseTimeString(record.ReportSTM)
		if err != nil {
			fmt.Printf("解析report_stm时间失败: %v\n", err)
			errorCount++
			continue
		}

		reportST, err := ParseTimeString(record.ReportST)
		if err != nil {
			fmt.Printf("解析report_st时间失败: %v\n", err)
			errorCount++
			continue
		}

		// 根据状态报告确定created_result值
		var newCreatedResult int
		if record.ReportSC == "DELIVRD" {
			newCreatedResult = 1 // 成功
		} else {
			newCreatedResult = 2 // 失败
		}

		// 更新发送记录
		err = dbManager.UpdateSendRecord(
			sendRecord.ID,
			newCreatedResult,
			reportSTM,
			reportST,
			record.ReportCHN,
			record.ReportSC,
		)

		if err != nil {
			fmt.Printf("更新发送记录失败: %v\n", err)
			errorCount++
			continue
		}

		fmt.Printf("更新成功: ID=%d, created_result=%d\n", sendRecord.ID, newCreatedResult)
		updatedCount++
		processedCount++
	}

	// 输出统计信息
	fmt.Println("\n=== 处理统计 ===")
	fmt.Printf("总记录数: %d\n", totalCount)
	fmt.Printf("成功更新: %d\n", updatedCount)
	fmt.Printf("跳过记录: %d\n", skippedCount)
	fmt.Printf("错误记录: %d\n", errorCount)
	fmt.Printf("处理完成: %d\n", processedCount)
}
