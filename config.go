package main

import (
	"fmt"
	"os"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string
	Port     string
	Username string
	Password string
	Database string
}

// GetDatabaseConfig 获取数据库配置
func GetDatabaseConfig() *DatabaseConfig {
	return &DatabaseConfig{
		Host:     getEnvOrDefault("DB_HOST", "*************"),
		Port:     getEnvOrDefault("DB_PORT", "44944"),
		Username: getEnvOrDefault("DB_USERNAME", "dxpt"),
		Password: getEnvOrDefault("DB_PASSWORD", "Rf&z6&Na7aWG"),
		Database: getEnvOrDefault("DB_DATABASE", "dxpt"),
	}
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		c.Username, c.Password, c.Host, c.Port, c.Database)
}

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
