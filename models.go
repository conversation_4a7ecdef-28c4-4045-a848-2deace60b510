package main

import (
	"time"
)

// SMSSendRecord 短信发送记录结构体
type SMSSendRecord struct {
	ID                   int64     `db:"id"`
	MemberID             int64     `db:"member_id"`
	GroupID              int64     `db:"group_id"`
	Mobile               string    `db:"mobile"`
	BatchNo              string    `db:"batch_no"`
	SMSType              int       `db:"sms_type"`
	SignText             string    `db:"sign_text"`
	ChannelID            int64     `db:"channel_id"`
	Content              string    `db:"content"`
	FeeNum               int       `db:"fee_num"`
	UnitPrice            float64   `db:"unit_price"`
	Source               int       `db:"source"`
	CreatedAt            time.Time `db:"created_at"`
	CreatedResult        int       `db:"created_result"`
	CT                   time.Time `db:"ct"`
	Result               string    `db:"result"`
	SID                  string    `db:"sid"`
	Seq                  string    `db:"seq"`
	ReportSTM            time.Time `db:"report_stm"`
	ReportST             time.Time `db:"report_st"`
	ReportSC             string    `db:"report_sc"`
	ReportCHN            string    `db:"report_chn"`
	FailedReason         string    `db:"failed_reason"`
	ReportTimeoutFlag    int       `db:"report_timeout_flag"`
}

// CSVRecord CSV记录结构体
type CSVRecord struct {
	Seq       string // 第1列：消息编号
	ReportSTM string // 第2列：发送时间
	Mobile    string // 第3列：手机号码
	ReportCHN string // 第6列：中文状态标识
	ReportSC  string // 第7列：状态报告
	ReportST  string // 第8列：状态时间
	BatchNo   string // 第11列：批次号
}
