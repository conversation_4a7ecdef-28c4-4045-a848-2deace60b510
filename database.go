package main

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	db *sql.DB
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(config *DatabaseConfig) (*DatabaseManager, error) {
	db, err := sql.Open("mysql", config.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	return &DatabaseManager{db: db}, nil
}

// Close 关闭数据库连接
func (dm *DatabaseManager) Close() error {
	return dm.db.Close()
}

// FindSendRecord 根据mobile、seq和batch_no查询发送记录
func (dm *DatabaseManager) FindSendRecord(mobile, seq, batchNo string) (*SMSSendRecord, error) {
	query := `
		SELECT id, member_id, group_id, mobile, batch_no, sms_type, sign_text, 
		       channel_id, content, fee_num, unit_price, source, created_at, 
		       created_result, ct, result, sid, seq, report_stm, report_st, 
		       report_sc, report_chn, failed_reason, report_timeout_flag
		FROM hg_dx_sms_send_record_202507 
		WHERE mobile = ? AND seq = ? AND batch_no = ?
	`

	row := dm.db.QueryRow(query, mobile, seq, batchNo)

	var record SMSSendRecord
	var reportSTM, reportST sql.NullTime
	err := row.Scan(
		&record.ID, &record.MemberID, &record.GroupID, &record.Mobile,
		&record.BatchNo, &record.SMSType, &record.SignText, &record.ChannelID,
		&record.Content, &record.FeeNum, &record.UnitPrice, &record.Source,
		&record.CreatedAt, &record.CreatedResult, &record.CT, &record.Result,
		&record.SID, &record.Seq, &reportSTM, &reportST,
		&record.ReportSC, &record.ReportCHN, &record.FailedReason,
		&record.ReportTimeoutFlag,
	)

	// 处理可能为NULL的时间字段
	if reportSTM.Valid {
		record.ReportSTM = reportSTM.Time
	}
	if reportST.Valid {
		record.ReportST = reportST.Time
	}

	if err == sql.ErrNoRows {
		return nil, nil // 记录不存在
	}
	if err != nil {
		return nil, fmt.Errorf("查询发送记录失败: %v", err)
	}

	return &record, nil
}

// UpdateSendRecord 更新发送记录的状态报告信息
func (dm *DatabaseManager) UpdateSendRecord(id int64, createdResult int, reportSTM, reportST time.Time, reportCHN, reportSC string) error {
	query := `
		UPDATE hg_dx_sms_send_record_202507
		SET created_result = ?, report_stm = ?, report_st = ?, report_chn = ?, report_sc = ?
		WHERE id = ?
	`

	result, err := dm.db.Exec(query, createdResult, reportSTM, reportST, reportCHN, reportSC, id)
	if err != nil {
		return fmt.Errorf("更新发送记录失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("没有记录被更新，ID: %d", id)
	}

	return nil
}

// ParseTimeString 解析时间字符串并减去8小时
func ParseTimeString(timeStr string) (time.Time, error) {
	// 尝试多种时间格式
	formats := []string{
		"20060102150405",     // 20250731185114
		"2006-01-02 15:04:05", // 2025-07-31 18:51:14
		"2006/01/02 15:04:05", // 2025/07/31 18:51:14
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			// 减去8小时
			adjustedTime := t.Add(-8 * time.Hour)
			return adjustedTime, nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析时间字符串: %s", timeStr)
}
