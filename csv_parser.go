package main

import (
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"strings"
)

// ParseCSVFile 解析CSV文件
func ParseCSVFile(filename string) ([]CSVRecord, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("无法打开CSV文件: %v", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = ','
	reader.TrimLeadingSpace = true

	var records []CSVRecord
	lineNum := 0

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("读取CSV文件第%d行时出错: %v", lineNum+1, err)
		}

		lineNum++

		// 确保记录有足够的列
		if len(record) < 11 {
			fmt.Printf("警告：第%d行列数不足，跳过\n", lineNum)
			continue
		}

		// 提取所需字段
		csvRecord := CSVRecord{
			Seq:       strings.TrimSpace(record[0]),  // 第1列
			ReportSTM: strings.TrimSpace(record[1]),  // 第2列
			Mobile:    strings.TrimSpace(record[2]),  // 第3列
			ReportCHN: strings.TrimSpace(record[5]),  // 第6列
			ReportSC:  strings.TrimSpace(record[6]),  // 第7列
			ReportST:  strings.TrimSpace(record[7]),  // 第8列
			BatchNo:   strings.TrimSpace(record[10]), // 第11列
		}

		// 跳过第六列为"未知"的状态报告
		if csvRecord.ReportCHN == "未知" {
			fmt.Printf("跳过未知状态报告，行号: %d\n", lineNum)
			continue
		}

		records = append(records, csvRecord)
	}

	fmt.Printf("成功解析CSV文件，共处理%d行，有效记录%d条\n", lineNum, len(records))
	return records, nil
}
