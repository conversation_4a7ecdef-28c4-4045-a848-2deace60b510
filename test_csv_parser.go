package main

import (
	"fmt"
	"log"
	"os"
)

// 测试CSV解析功能的独立程序
func testCSVParser() {
	fmt.Println("测试CSV解析功能...")
	
	records, err := ParseCSVFile("sample_status_report.csv")
	if err != nil {
		log.Fatalf("解析CSV文件失败: %v", err)
	}

	fmt.Printf("解析到%d条记录:\n", len(records))
	for i, record := range records {
		fmt.Printf("记录%d:\n", i+1)
		fmt.Printf("  Seq: %s\n", record.Seq)
		fmt.Printf("  Mobile: %s\n", record.Mobile)
		fmt.Printf("  BatchNo: %s\n", record.BatchNo)
		fmt.Printf("  ReportCHN: %s\n", record.ReportCHN)
		fmt.Printf("  ReportSC: %s\n", record.ReportSC)
		fmt.Printf("  ReportSTM: %s\n", record.ReportSTM)
		fmt.Printf("  ReportST: %s\n", record.ReportST)
		fmt.Println()
	}

	// 测试时间解析
	fmt.Println("测试时间解析功能...")
	testTimeStr := "20250731185114"
	parsedTime, err := ParseTimeString(testTimeStr)
	if err != nil {
		fmt.Printf("时间解析失败: %v\n", err)
	} else {
		fmt.Printf("时间解析成功: %s -> %s\n", testTimeStr, parsedTime.Format("2006-01-02 15:04:05"))
	}
}

func init() {
	// 如果程序以test模式运行，执行测试
	if len(os.Args) > 1 && os.Args[1] == "test" {
		testCSVParser()
		os.Exit(0)
	}
}
